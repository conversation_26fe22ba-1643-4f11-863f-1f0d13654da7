<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

$message = trim($_POST['message'] ?? '');

if (empty($message)) {
    http_response_code(400);
    echo json_encode(['error' => 'Message cannot be empty']);
    exit();
}

// Sanitize message (basic HTML removal)
$message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');

// Limit message length
if (strlen($message) > 500) {
    http_response_code(400);
    echo json_encode(['error' => 'Message too long (max 500 characters)']);
    exit();
}

$alumni_id = $_SESSION['user_id'];

// Insert message into database
$stmt = $conn->prepare("INSERT INTO chat_messages (alumni_id, message) VALUES (?, ?)");
$stmt->bind_param("is", $alumni_id, $message);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to send message']);
}

$stmt->close();
$conn->close();
?>
