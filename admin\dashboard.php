<?php
require_once '../includes/session.php';
require_admin();
require_once '../includes/db_connect.php';

// Fetch comprehensive stats for the dashboard
$stats = [];

// Get actual counts
$result = $conn->query("SELECT COUNT(*) AS count FROM alumni_users WHERE is_verified = 0");
$stats['pending_users'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM alumni_users WHERE is_verified = 1");
$stats['verified_alumni'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM alumni_users");
$stats['total_alumni'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM events");
$stats['total_events'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM events WHERE date >= CURDATE()");
$stats['upcoming_events'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM news");
$stats['total_news'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM feedback");
$stats['total_feedback'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM chat_messages WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stats['recent_messages'] = $result ? $result->fetch_assoc()['count'] : 0;

$result = $conn->query("SELECT COUNT(*) AS count FROM media_gallery");
$stats['gallery_images'] = $result ? $result->fetch_assoc()['count'] : 0;

// Get recent activities
$recent_activities = [];

// Recent registrations
$stmt = $conn->prepare("SELECT full_name, created_at FROM alumni_users WHERE is_verified = 0 ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $recent_activities[] = [
        'type' => 'registration',
        'message' => $row['full_name'] . ' registered',
        'time' => $row['created_at']
    ];
}
$stmt->close();

// Recent feedback
$stmt = $conn->prepare("SELECT au.full_name, f.created_at FROM feedback f JOIN alumni_users au ON f.alumni_id = au.id ORDER BY f.created_at DESC LIMIT 3");
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $recent_activities[] = [
        'type' => 'feedback',
        'message' => $row['full_name'] . ' submitted feedback',
        'time' => $row['created_at']
    ];
}
$stmt->close();

// Sort activities by time
usort($recent_activities, function($a, $b) {
    return strtotime($b['time']) - strtotime($a['time']);
});
$recent_activities = array_slice($recent_activities, 0, 8);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #1E40AF, #1E3A8A);
        }
    </style>
</head>
<body class="bg-gray-200 flex">

    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-white min-h-screen p-4">
        <h2 class="text-2xl font-bold mb-10">Admin Panel</h2>
        <nav class="space-y-4">
            <a href="dashboard.php" class="block bg-gray-700 p-2 rounded">Dashboard</a>
            <a href="approve_users.php" class="block hover:bg-gray-700 p-2 rounded">Approve Alumni</a>
            <a href="create_event.php" class="block hover:bg-gray-700 p-2 rounded">Create Event</a>
            <a href="post_news.php" class="block hover:bg-gray-700 p-2 rounded">Post News</a>
            <a href="manage_gallery.php" class="block hover:bg-gray-700 p-2 rounded">Manage Gallery</a>
            <a href="view_feedback.php" class="block hover:bg-gray-700 p-2 rounded">View Feedback</a>
            <a href="../logout.php" class="block hover:bg-gray-700 p-2 rounded mt-10">Logout</a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-10">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Admin Dashboard</h1>
            <p class="text-gray-600">Manage the Alumni Portal system</p>
        </div>

        <!-- Statistics Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Pending Approvals -->
            <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-3xl font-bold"><?php echo $stats['pending_users']; ?></h3>
                        <p class="text-yellow-100">Pending Approvals</p>
                    </div>
                    <div class="text-4xl opacity-80">⏳</div>
                </div>
                <a href="approve_users.php" class="mt-4 inline-block font-semibold hover:underline text-yellow-100">
                    Review Applications →
                </a>
            </div>

            <!-- Verified Alumni -->
            <div class="bg-gradient-to-r from-green-400 to-green-600 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-3xl font-bold"><?php echo $stats['verified_alumni']; ?></h3>
                        <p class="text-green-100">Verified Alumni</p>
                    </div>
                    <div class="text-4xl opacity-80">✅</div>
                </div>
            </div>

            <!-- Total Events -->
            <div class="bg-gradient-to-r from-blue-400 to-blue-600 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-3xl font-bold"><?php echo $stats['total_events']; ?></h3>
                        <p class="text-blue-100">Total Events</p>
                    </div>
                    <div class="text-4xl opacity-80">📅</div>
                </div>
            </div>

            <!-- Recent Messages -->
            <div class="bg-gradient-to-r from-purple-400 to-purple-600 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-3xl font-bold"><?php echo $stats['recent_messages']; ?></h3>
                        <p class="text-purple-100">Messages (7 days)</p>
                    </div>
                    <div class="text-4xl opacity-80">💬</div>
                </div>
            </div>
        </div>

        <!-- Secondary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-indigo-600"><?php echo $stats['total_news']; ?></div>
                <div class="text-sm text-gray-600">News Articles</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-pink-600"><?php echo $stats['total_feedback']; ?></div>
                <div class="text-sm text-gray-600">Feedback Received</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-teal-600"><?php echo $stats['gallery_images']; ?></div>
                <div class="text-sm text-gray-600">Gallery Images</div>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
            <!-- Quick Actions -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    🚀 Quick Actions
                </h2>
                <div class="grid grid-cols-2 gap-4">
                    <a href="create_event.php" class="bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                        <div class="text-2xl mb-2">📅</div>
                        <div class="font-semibold">Create Event</div>
                    </a>
                    <a href="post_news.php" class="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition duration-300 text-center">
                        <div class="text-2xl mb-2">📰</div>
                        <div class="font-semibold">Post News</div>
                    </a>
                    <a href="manage_gallery.php" class="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition duration-300 text-center">
                        <div class="text-2xl mb-2">🖼️</div>
                        <div class="font-semibold">Manage Gallery</div>
                    </a>
                    <a href="view_feedback.php" class="bg-orange-600 text-white p-4 rounded-lg hover:bg-orange-700 transition duration-300 text-center">
                        <div class="text-2xl mb-2">💬</div>
                        <div class="font-semibold">View Feedback</div>
                    </a>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    📊 Recent Activity
                </h2>
                <?php if (empty($recent_activities)): ?>
                    <p class="text-gray-600">No recent activity to display.</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-800"><?php echo htmlspecialchars($activity['message']); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo date('M j, g:i A', strtotime($activity['time'])); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

</body>
</html>
