<?php
require_once '../includes/session.php';
require_admin();
require_once '../includes/db_connect.php';

// Fetch stats for the dashboard (placeholders for now)
$pending_users = 0;
$total_alumni = 0;
$total_events = 0;

// Get actual counts
$result = $conn->query("SELECT COUNT(*) AS count FROM alumni_users WHERE is_verified = 0");
if ($result) $pending_users = $result->fetch_assoc()['count'];

$result = $conn->query("SELECT COUNT(*) AS count FROM alumni_users");
if ($result) $total_alumni = $result->fetch_assoc()['count'];

$result = $conn->query("SELECT COUNT(*) AS count FROM events");
if ($result) $total_events = $result->fetch_assoc()['count'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #1E40AF, #1E3A8A);
        }
    </style>
</head>
<body class="bg-gray-200 flex">

    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-white min-h-screen p-4">
        <h2 class="text-2xl font-bold mb-10">Admin Panel</h2>
        <nav class="space-y-4">
            <a href="dashboard.php" class="block bg-gray-700 p-2 rounded">Dashboard</a>
            <a href="approve_users.php" class="block hover:bg-gray-700 p-2 rounded">Approve Alumni</a>
            <a href="create_event.php" class="block hover:bg-gray-700 p-2 rounded">Create Event</a>
            <a href="post_news.php" class="block hover:bg-gray-700 p-2 rounded">Post News</a>
            <a href="manage_gallery.php" class="block hover:bg-gray-700 p-2 rounded">Manage Gallery</a>
            <a href="view_feedback.php" class="block hover:bg-gray-700 p-2 rounded">View Feedback</a>
            <a href="../logout.php" class="block hover:bg-gray-700 p-2 rounded mt-10">Logout</a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-10">
        <h1 class="text-4xl font-bold text-gray-800 mb-8">Admin Dashboard</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Stat Card: Pending Approvals -->
            <div class="bg-yellow-400 text-white p-6 rounded-lg shadow-lg">
                <h3 class="text-2xl font-bold"><?php echo $pending_users; ?></h3>
                <p class="text-lg">Pending Approvals</p>
                <a href="approve_users.php" class="mt-4 inline-block font-semibold hover:underline">View &rarr;</a>
            </div>

            <!-- Stat Card: Total Alumni -->
            <div class="bg-green-500 text-white p-6 rounded-lg shadow-lg">
                <h3 class="text-2xl font-bold"><?php echo $total_alumni; ?></h3>
                <p class="text-lg">Total Alumni</p>
            </div>

            <!-- Stat Card: Total Events -->
            <div class="bg-blue-500 text-white p-6 rounded-lg shadow-lg">
                <h3 class="text-2xl font-bold"><?php echo $total_events; ?></h3>
                <p class="text-lg">Total Events</p>
            </div>
        </div>

        <div class="mt-10 bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Quick Actions</h2>
            <div class="flex space-x-4">
                <a href="create_event.php" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">Create New Event</a>
                <a href="post_news.php" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">Publish News</a>
            </div>
        </div>
    </main>

</body>
</html>
