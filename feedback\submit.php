<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

$errors = [];
$success_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $message = trim($_POST['message']);
    $alumni_id = $_SESSION['user_id'];

    if (empty($message)) {
        $errors[] = "Feedback message is required.";
    } elseif (strlen($message) > 1000) {
        $errors[] = "Feedback message is too long (maximum 1000 characters).";
    }

    if (empty($errors)) {
        $stmt = $conn->prepare("INSERT INTO feedback (alumni_id, message) VALUES (?, ?)");
        $stmt->bind_param("is", $alumni_id, $message);

        if ($stmt->execute()) {
            $success_message = "Thank you for your feedback! We appreciate your input and will review it carefully.";
            $_POST['message'] = ''; // Clear the form
        } else {
            $errors[] = "Failed to submit feedback. Please try again.";
        }
        $stmt->close();
    }
}

// Get user info for display
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT full_name FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($full_name);
$stmt->fetch();
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Feedback - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-4xl font-bold text-gray-800">Submit Feedback</h1>
                <p class="text-gray-600 mt-2">Help us improve the Alumni Portal with your suggestions</p>
            </div>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <div class="max-w-2xl mx-auto">
            <!-- Success Message -->
            <?php if (!empty($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">✅</span>
                        <div>
                            <strong>Success!</strong>
                            <p><?php echo htmlspecialchars($success_message); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">❌</span>
                        <div>
                            <strong>Error!</strong>
                            <ul class="mt-1">
                                <?php foreach ($errors as $error): ?>
                                    <li>• <?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Feedback Form -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                    <h2 class="text-2xl font-bold mb-2">We Value Your Opinion</h2>
                    <p class="text-blue-100">Your feedback helps us create a better experience for all alumni</p>
                </div>

                <div class="p-6">
                    <form action="submit.php" method="post" class="space-y-6">
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                Your Feedback or Suggestions
                            </label>
                            <textarea name="message"
                                      id="message"
                                      rows="8"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="Please share your thoughts, suggestions, or report any issues you've encountered..."
                                      maxlength="1000"
                                      required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                            <div class="mt-2 flex justify-between items-center">
                                <span class="text-xs text-gray-500">
                                    <span id="char-count">0</span>/1000 characters
                                </span>
                                <span class="text-xs text-gray-500">
                                    Submitted by: <?php echo htmlspecialchars($full_name); ?>
                                </span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between pt-4">
                            <button type="submit"
                                    class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300 font-semibold">
                                📤 Submit Feedback
                            </button>
                            <button type="reset"
                                    class="bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 transition duration-300">
                                Clear Form
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Feedback Guidelines -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="font-semibold text-blue-800 mb-3">💡 Feedback Guidelines</h3>
                <div class="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
                    <div>
                        <h4 class="font-semibold mb-2">What to include:</h4>
                        <ul class="space-y-1">
                            <li>• Specific suggestions for improvement</li>
                            <li>• Bug reports with details</li>
                            <li>• Feature requests</li>
                            <li>• User experience feedback</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">Please note:</h4>
                        <ul class="space-y-1">
                            <li>• All feedback is reviewed by administrators</li>
                            <li>• We may contact you for clarification</li>
                            <li>• Response time: 3-5 business days</li>
                            <li>• Keep feedback constructive and respectful</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Character counter
        const messageTextarea = document.getElementById('message');
        const charCount = document.getElementById('char-count');

        function updateCharCount() {
            const length = messageTextarea.value.length;
            charCount.textContent = length;

            if (length >= 950) {
                charCount.classList.add('text-red-500');
            } else if (length >= 800) {
                charCount.classList.add('text-yellow-500');
                charCount.classList.remove('text-red-500');
            } else {
                charCount.classList.remove('text-red-500', 'text-yellow-500');
            }
        }

        messageTextarea.addEventListener('input', updateCharCount);

        // Initialize character count
        updateCharCount();
    </script>
</body>
</html>
