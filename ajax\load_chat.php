<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Fetch recent chat messages with user information
$stmt = $conn->prepare("
    SELECT cm.message, cm.sent_at, au.full_name, au.department, au.year_of_graduation, cm.alumni_id
    FROM chat_messages cm
    JOIN alumni_users au ON cm.alumni_id = au.id
    WHERE au.is_verified = 1
    ORDER BY cm.sent_at DESC
    LIMIT 50
");
$stmt->execute();
$result = $stmt->get_result();
$messages = array_reverse($result->fetch_all(MYSQLI_ASSOC)); // Reverse to show oldest first
$stmt->close();

$current_user_id = $_SESSION['user_id'];

if (empty($messages)) {
    echo '<div class="text-center text-gray-500 py-8">';
    echo '<div class="text-4xl mb-2">💬</div>';
    echo '<p>No messages yet. Be the first to start the conversation!</p>';
    echo '</div>';
} else {
    foreach ($messages as $message) {
        $is_own_message = ($message['alumni_id'] == $current_user_id);
        $message_class = $is_own_message ? 'ml-auto bg-blue-500 text-white' : 'mr-auto bg-gray-200 text-gray-800';
        $alignment_class = $is_own_message ? 'justify-end' : 'justify-start';

        echo '<div class="flex ' . $alignment_class . ' mb-4">';
        echo '<div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ' . $message_class . '">';

        if (!$is_own_message) {
            echo '<div class="text-xs font-semibold mb-1">';
            echo htmlspecialchars($message['full_name']);
            echo ' <span class="font-normal text-gray-600">(' . htmlspecialchars($message['department']) . ' \'' . htmlspecialchars($message['year_of_graduation']) . ')</span>';
            echo '</div>';
        }

        echo '<div class="break-words">' . nl2br(htmlspecialchars($message['message'])) . '</div>';

        echo '<div class="text-xs mt-1 ' . ($is_own_message ? 'text-blue-100' : 'text-gray-500') . '">';
        echo date('M j, g:i A', strtotime($message['sent_at']));
        echo '</div>';

        echo '</div>';
        echo '</div>';
    }
}
?>
