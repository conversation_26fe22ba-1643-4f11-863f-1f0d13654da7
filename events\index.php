<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Fetch all events, ordered by date
$stmt = $conn->prepare("SELECT id, title, description, date, image_path FROM events ORDER BY date ASC");
$stmt->execute();
$result = $stmt->get_result();
$events = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Separate upcoming and past events
$upcoming_events = [];
$past_events = [];
$today = date('Y-m-d');

foreach ($events as $event) {
    if ($event['date'] >= $today) {
        $upcoming_events[] = $event;
    } else {
        $past_events[] = $event;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">Events</h1>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <!-- Upcoming Events Section -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm mr-3">Upcoming</span>
                Upcoming Events
            </h2>

            <?php if (empty($upcoming_events)): ?>
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <div class="text-gray-500 text-6xl mb-4">📅</div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">No Upcoming Events</h3>
                    <p class="text-gray-600">There are currently no upcoming events scheduled. Check back later!</p>
                </div>
            <?php else: ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($upcoming_events as $event): ?>
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden">
                            <?php if ($event['image_path']): ?>
                                <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($event['title']); ?>"
                                     class="w-full h-48 object-cover">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                    <span class="text-white text-4xl">📅</span>
                                </div>
                            <?php endif; ?>

                            <div class="p-6">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-xl font-bold text-gray-800"><?php echo htmlspecialchars($event['title']); ?></h3>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Upcoming</span>
                                </div>

                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    <?php echo htmlspecialchars($event['description']); ?>
                                </p>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 flex items-center">
                                        📅 <?php echo date('M j, Y', strtotime($event['date'])); ?>
                                    </span>
                                    <a href="event_details.php?id=<?php echo $event['id']; ?>"
                                       class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300 text-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </section>

        <!-- Past Events Section -->
        <?php if (!empty($past_events)): ?>
            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <span class="bg-gray-500 text-white px-3 py-1 rounded-full text-sm mr-3">Past</span>
                    Past Events
                </h2>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach (array_slice($past_events, 0, 6) as $event): ?>
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden opacity-75">
                            <?php if ($event['image_path']): ?>
                                <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($event['title']); ?>"
                                     class="w-full h-48 object-cover">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center">
                                    <span class="text-white text-4xl">📅</span>
                                </div>
                            <?php endif; ?>

                            <div class="p-6">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-xl font-bold text-gray-800"><?php echo htmlspecialchars($event['title']); ?></h3>
                                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Past</span>
                                </div>

                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    <?php echo htmlspecialchars($event['description']); ?>
                                </p>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 flex items-center">
                                        📅 <?php echo date('M j, Y', strtotime($event['date'])); ?>
                                    </span>
                                    <a href="event_details.php?id=<?php echo $event['id']; ?>"
                                       class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition duration-300 text-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
