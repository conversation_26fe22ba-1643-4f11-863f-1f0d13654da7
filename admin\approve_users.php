<?php
require_once '../includes/session.php';
require_admin();
require_once '../includes/db_connect.php';

// Handle approval/denial actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve'])) {
        $user_id = $_POST['user_id'];
        $stmt = $conn->prepare("UPDATE alumni_users SET is_verified = 1 WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
    } elseif (isset($_POST['deny'])) {
        $user_id = $_POST['user_id'];
        // Optional: Delete the user record or just keep it as unverified
        $stmt = $conn->prepare("DELETE FROM alumni_users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
    }
    header("Location: approve_users.php");
    exit();
}

// Fetch pending users
$pending_users = [];
$result = $conn->query("SELECT id, full_name, email, matric_no, department, year_of_graduation, certificate_path FROM alumni_users WHERE is_verified = 0 ORDER BY created_at ASC");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $pending_users[] = $row;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approve Alumni</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-200 flex">

    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-white min-h-screen p-4">
        <h2 class="text-2xl font-bold mb-10">Admin Panel</h2>
        <nav class="space-y-4">
            <a href="dashboard.php" class="block hover:bg-gray-700 p-2 rounded">Dashboard</a>
            <a href="approve_users.php" class="block bg-gray-700 p-2 rounded">Approve Alumni</a>
            <a href="create_event.php" class="block hover:bg-gray-700 p-2 rounded">Create Event</a>
            <a href="post_news.php" class="block hover:bg-gray-700 p-2 rounded">Post News</a>
            <a href="manage_gallery.php" class="block hover:bg-gray-700 p-2 rounded">Manage Gallery</a>
            <a href="view_feedback.php" class="block hover:bg-gray-700 p-2 rounded">View Feedback</a>
            <a href="../logout.php" class="block hover:bg-gray-700 p-2 rounded mt-10">Logout</a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-10">
        <h1 class="text-4xl font-bold text-gray-800 mb-8">Pending Alumni Approvals</h1>

        <div class="bg-white p-6 rounded-lg shadow-md">
            <?php if (empty($pending_users)): ?>
                <p class="text-gray-600">There are no pending registrations to approve.</p>
            <?php else: ?>
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-800 text-white">
                        <tr>
                            <th class="w-1/4 py-3 px-4 uppercase font-semibold text-sm">Full Name</th>
                            <th class="w-1/4 py-3 px-4 uppercase font-semibold text-sm">Matric No</th>
                            <th class="w-1/4 py-3 px-4 uppercase font-semibold text-sm">Department</th>
                            <th class="py-3 px-4 uppercase font-semibold text-sm">Certificate</th>
                            <th class="py-3 px-4 uppercase font-semibold text-sm">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <?php foreach ($pending_users as $user): ?>
                            <tr>
                                <td class="w-1/4 py-3 px-4"><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td class="w-1/4 py-3 px-4"><?php echo htmlspecialchars($user['matric_no']); ?></td>
                                <td class="w-1/4 py-3 px-4"><?php echo htmlspecialchars($user['department']); ?></td>
                                <td class="py-3 px-4"><a href="../<?php echo htmlspecialchars($user['certificate_path']); ?>" target="_blank" class="text-blue-600 hover:underline">View</a></td>
                                <td class="py-3 px-4">
                                    <form method="POST" class="inline-block">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="approve" class="bg-green-500 text-white py-1 px-3 rounded hover:bg-green-600">Approve</button>
                                    </form>
                                    <form method="POST" class="inline-block">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="deny" class="bg-red-500 text-white py-1 px-3 rounded hover:bg-red-600" onclick="return confirm('Are you sure you want to deny this user?');">Deny</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </main>

</body>
</html>
