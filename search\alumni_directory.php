<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Get search parameters
$search_name = isset($_GET['search_name']) ? trim($_GET['search_name']) : '';
$search_department = isset($_GET['search_department']) ? trim($_GET['search_department']) : '';
$search_year = isset($_GET['search_year']) ? trim($_GET['search_year']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build search query
$where_conditions = ["au.is_verified = 1", "au.visibility = 'public'"];
$params = [];
$param_types = "";

if (!empty($search_name)) {
    $where_conditions[] = "au.full_name LIKE ?";
    $params[] = "%$search_name%";
    $param_types .= "s";
}

if (!empty($search_department)) {
    $where_conditions[] = "au.department LIKE ?";
    $params[] = "%$search_department%";
    $param_types .= "s";
}

if (!empty($search_year)) {
    $where_conditions[] = "au.year_of_graduation = ?";
    $params[] = $search_year;
    $param_types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM alumni_users au WHERE $where_clause";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$count_stmt->close();

$total_pages = ceil($total_records / $per_page);

// Get alumni records
$sql = "SELECT au.id, au.full_name, au.department, au.year_of_graduation, au.job_title, au.location, au.linkedin
        FROM alumni_users au
        WHERE $where_clause
        ORDER BY au.full_name ASC
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
$params[] = $per_page;
$params[] = $offset;
$param_types .= "ii";
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$alumni = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get unique departments for filter dropdown
$dept_stmt = $conn->prepare("SELECT DISTINCT department FROM alumni_users WHERE is_verified = 1 ORDER BY department");
$dept_stmt->execute();
$departments = $dept_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$dept_stmt->close();

// Get unique years for filter dropdown
$year_stmt = $conn->prepare("SELECT DISTINCT year_of_graduation FROM alumni_users WHERE is_verified = 1 ORDER BY year_of_graduation DESC");
$year_stmt->execute();
$years = $year_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$year_stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Directory - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-4xl font-bold text-gray-800">Alumni Directory</h1>
                <p class="text-gray-600 mt-2">Connect with fellow alumni from Polytechnic Ilaro</p>
            </div>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <form method="GET" action="" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search_name" class="block text-sm font-medium text-gray-700 mb-2">Search by Name</label>
                    <input type="text"
                           id="search_name"
                           name="search_name"
                           value="<?php echo htmlspecialchars($search_name); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter name...">
                </div>

                <div>
                    <label for="search_department" class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                    <select id="search_department"
                            name="search_department"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Departments</option>
                        <?php foreach ($departments as $dept): ?>
                            <option value="<?php echo htmlspecialchars($dept['department']); ?>"
                                    <?php echo ($search_department === $dept['department']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($dept['department']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="search_year" class="block text-sm font-medium text-gray-700 mb-2">Graduation Year</label>
                    <select id="search_year"
                            name="search_year"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Years</option>
                        <?php foreach ($years as $year): ?>
                            <option value="<?php echo htmlspecialchars($year['year_of_graduation']); ?>"
                                    <?php echo ($search_year === $year['year_of_graduation']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($year['year_of_graduation']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300 font-semibold">
                        🔍 Search
                    </button>
                </div>
            </form>

            <?php if (!empty($search_name) || !empty($search_department) || !empty($search_year)): ?>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">
                            Found <?php echo $total_records; ?> alumni matching your search
                        </span>
                        <a href="alumni_directory.php" class="text-sm text-blue-600 hover:underline">Clear filters</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Alumni Results -->
        <?php if (empty($alumni)): ?>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-gray-500 text-6xl mb-4">👥</div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">No Alumni Found</h3>
                <p class="text-gray-600">
                    <?php if (!empty($search_name) || !empty($search_department) || !empty($search_year)): ?>
                        No alumni match your search criteria. Try adjusting your filters.
                    <?php else: ?>
                        No verified alumni profiles are currently available.
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <!-- Results Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <?php foreach ($alumni as $alumnus): ?>
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden">
                        <!-- Profile Header -->
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                            <div class="flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4">
                                <span class="text-2xl font-bold">
                                    <?php echo strtoupper(substr($alumnus['full_name'], 0, 1)); ?>
                                </span>
                            </div>
                            <h3 class="text-xl font-bold text-center">
                                <?php echo htmlspecialchars($alumnus['full_name']); ?>
                            </h3>
                        </div>

                        <!-- Profile Details -->
                        <div class="p-6">
                            <div class="space-y-3">
                                <div class="flex items-center text-gray-600">
                                    <span class="text-blue-500 mr-2">🎓</span>
                                    <span class="text-sm">
                                        <?php echo htmlspecialchars($alumnus['department']); ?>
                                    </span>
                                </div>

                                <div class="flex items-center text-gray-600">
                                    <span class="text-green-500 mr-2">📅</span>
                                    <span class="text-sm">
                                        Class of <?php echo htmlspecialchars($alumnus['year_of_graduation']); ?>
                                    </span>
                                </div>

                                <?php if (!empty($alumnus['job_title'])): ?>
                                    <div class="flex items-center text-gray-600">
                                        <span class="text-purple-500 mr-2">💼</span>
                                        <span class="text-sm">
                                            <?php echo htmlspecialchars($alumnus['job_title']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($alumnus['location'])): ?>
                                    <div class="flex items-center text-gray-600">
                                        <span class="text-red-500 mr-2">📍</span>
                                        <span class="text-sm">
                                            <?php echo htmlspecialchars($alumnus['location']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="flex space-x-2">
                                    <?php if (!empty($alumnus['linkedin'])): ?>
                                        <a href="<?php echo htmlspecialchars($alumnus['linkedin']); ?>"
                                           target="_blank"
                                           class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded-md hover:bg-blue-700 transition duration-300 text-sm">
                                            LinkedIn
                                        </a>
                                    <?php endif; ?>
                                    <button class="flex-1 bg-gray-600 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition duration-300 text-sm">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            Showing <?php echo (($page - 1) * $per_page) + 1; ?> to
                            <?php echo min($page * $per_page, $total_records); ?> of
                            <?php echo $total_records; ?> results
                        </div>

                        <div class="flex space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-300">
                                    ← Previous
                                </a>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="px-3 py-2 <?php echo ($i == $page) ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?> rounded-md transition duration-300">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-300">
                                    Next →
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
