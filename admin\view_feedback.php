<?php
require_once '../includes/session.php';
require_admin();
require_once '../includes/db_connect.php';

// Fetch feedback
$feedback = [];
$sql = "SELECT f.id, f.message, f.created_at, a.full_name, a.email 
        FROM feedback f 
        JOIN alumni_users a ON f.alumni_id = a.id 
        ORDER BY f.created_at DESC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $feedback[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Feedback</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-200 flex">

    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-white min-h-screen p-4">
        <h2 class="text-2xl font-bold mb-10">Admin Panel</h2>
        <nav class="space-y-4">
            <a href="dashboard.php" class="block hover:bg-gray-700 p-2 rounded">Dashboard</a>
            <a href="approve_users.php" class="block hover:bg-gray-700 p-2 rounded">Approve Alumni</a>
            <a href="create_event.php" class="block hover:bg-gray-700 p-2 rounded">Create Event</a>
            <a href="post_news.php" class="block hover:bg-gray-700 p-2 rounded">Post News</a>
            <a href="manage_gallery.php" class="block hover:bg-gray-700 p-2 rounded">Manage Gallery</a>
            <a href="view_feedback.php" class="block bg-gray-700 p-2 rounded">View Feedback</a>
            <a href="../logout.php" class="block hover:bg-gray-700 p-2 rounded mt-10">Logout</a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-10">
        <h1 class="text-4xl font-bold text-gray-800 mb-8">Alumni Feedback</h1>

        <div class="bg-white p-6 rounded-lg shadow-md">
            <?php if (empty($feedback)): ?>
                <p class="text-gray-600">There is no feedback to display.</p>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($feedback as $item): ?>
                        <div class="border-b pb-4">
                            <p class="text-gray-800"><?php echo htmlspecialchars($item['message']); ?></p>
                            <p class="text-sm text-gray-500 mt-2">
                                By <?php echo htmlspecialchars($item['full_name']); ?> (<?php echo htmlspecialchars($item['email']); ?>) on <?php echo date('M d, Y H:i', strtotime($item['created_at'])); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

</body>
</html>
