<?php
require_once '../includes/session.php';
require_admin();
require_once '../includes/db_connect.php';

// Fetch feedback
$feedback = [];
$sql = "SELECT f.id, f.message, f.created_at, a.full_name, a.email 
        FROM feedback f 
        JOIN alumni_users a ON f.alumni_id = a.id 
        ORDER BY f.created_at DESC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $feedback[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Feedback</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-200 flex">

    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-white min-h-screen p-4">
        <h2 class="text-2xl font-bold mb-10">Admin Panel</h2>
        <nav class="space-y-4">
            <a href="dashboard.php" class="block hover:bg-gray-700 p-2 rounded">Dashboard</a>
            <a href="approve_users.php" class="block hover:bg-gray-700 p-2 rounded">Approve Alumni</a>
            <a href="create_event.php" class="block hover:bg-gray-700 p-2 rounded">Create Event</a>
            <a href="post_news.php" class="block hover:bg-gray-700 p-2 rounded">Post News</a>
            <a href="manage_gallery.php" class="block hover:bg-gray-700 p-2 rounded">Manage Gallery</a>
            <a href="view_feedback.php" class="block bg-gray-700 p-2 rounded">View Feedback</a>
            <a href="../logout.php" class="block hover:bg-gray-700 p-2 rounded mt-10">Logout</a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-10">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">Alumni Feedback</h1>
            <div class="text-sm text-gray-600">
                Total Feedback: <span class="font-semibold"><?php echo count($feedback); ?></span>
            </div>
        </div>

        <?php if (empty($feedback)): ?>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-gray-500 text-6xl mb-4">💬</div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">No Feedback Available</h3>
                <p class="text-gray-600">No alumni have submitted feedback yet.</p>
            </div>
        <?php else: ?>
            <div class="space-y-6">
                <?php foreach ($feedback as $index => $item): ?>
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300">
                        <div class="p-6">
                            <!-- Feedback Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3">
                                        <?php echo strtoupper(substr($item['full_name'], 0, 1)); ?>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">
                                            <?php echo htmlspecialchars($item['full_name']); ?>
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($item['email']); ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                        #<?php echo str_pad($item['id'], 3, '0', STR_PAD_LEFT); ?>
                                    </span>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <?php echo date('M j, Y \a\t g:i A', strtotime($item['created_at'])); ?>
                                    </p>
                                </div>
                            </div>

                            <!-- Feedback Content -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-gray-800 leading-relaxed">
                                    <?php echo nl2br(htmlspecialchars($item['message'])); ?>
                                </p>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-4 flex justify-between items-center">
                                <div class="text-xs text-gray-500">
                                    Submitted <?php echo date('n/j/Y', strtotime($item['created_at'])); ?>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition duration-300">
                                        Mark as Reviewed
                                    </button>
                                    <button class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition duration-300">
                                        Reply
                                    </button>
                                    <button class="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition duration-300">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination placeholder for future enhancement -->
            <?php if (count($feedback) > 10): ?>
                <div class="mt-8 text-center">
                    <p class="text-gray-600">Showing all feedback. Pagination will be added for better performance.</p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>

</body>
</html>
