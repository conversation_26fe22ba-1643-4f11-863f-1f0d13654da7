<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Get current user info for display
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT full_name, department, year_of_graduation FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($full_name, $department, $year_of_graduation);
$stmt->fetch();
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Chat Room - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
        #chat-messages {
            scroll-behavior: smooth;
        }
        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-4xl font-bold text-gray-800">Alumni Chat Room</h1>
                <p class="text-gray-600 mt-2">Connect with fellow alumni in real-time</p>
            </div>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Chat Header -->
            <div class="bg-gray-50 px-6 py-4 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">General Discussion</h3>
                        <p class="text-sm text-gray-600">Welcome, <?php echo htmlspecialchars($full_name); ?>!</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                        <span class="text-sm text-gray-600">Online</span>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div id="chat-messages" class="h-96 overflow-y-auto p-6 bg-gray-50">
                <!-- Chat messages will be loaded here via AJAX -->
                <div class="text-center text-gray-500">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p>Loading messages...</p>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator px-6">
                Someone is typing...
            </div>

            <!-- Message Input -->
            <div class="bg-white px-6 py-4 border-t">
                <form id="chat-form" class="flex space-x-4">
                    <div class="flex-1">
                        <input type="text"
                               id="message"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Type your message here..."
                               maxlength="500"
                               autocomplete="off">
                    </div>
                    <button type="submit"
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                            id="send-button">
                        Send 📤
                    </button>
                </form>
                <div class="mt-2 text-xs text-gray-500">
                    <span id="char-count">0</span>/500 characters
                </div>
            </div>
        </div>

        <!-- Chat Guidelines -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 mb-2">💡 Chat Guidelines</h4>
            <ul class="text-sm text-blue-700 space-y-1">
                <li>• Be respectful and professional in your communications</li>
                <li>• Keep discussions relevant to alumni activities and networking</li>
                <li>• No spam, offensive language, or inappropriate content</li>
                <li>• Messages are limited to 500 characters</li>
            </ul>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script>
        // Character counter
        const messageInput = document.getElementById('message');
        const charCount = document.getElementById('char-count');
        const sendButton = document.getElementById('send-button');

        messageInput.addEventListener('input', function() {
            const length = this.value.length;
            charCount.textContent = length;

            if (length >= 500) {
                charCount.classList.add('text-red-500');
                sendButton.disabled = true;
            } else {
                charCount.classList.remove('text-red-500');
                sendButton.disabled = false;
            }
        });

        // Auto-focus on message input
        messageInput.focus();
    </script>
</body>
</html>
