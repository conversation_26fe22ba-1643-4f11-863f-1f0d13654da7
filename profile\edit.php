<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

$user_id = $_SESSION['user_id'];
$errors = [];
$success_message = '';

// Fetch current user data
$stmt = $conn->prepare("SELECT * FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user) {
    header('Location: ../logout.php');
    exit();
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $job_title = trim($_POST['job_title']);
    $linkedin = trim($_POST['linkedin']);
    $location = trim($_POST['location']);
    $visibility = $_POST['visibility'];

    // Validation
    if (empty($full_name)) {
        $errors[] = "Full name is required.";
    }

    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format.";
    }

    // Check if email is already taken by another user
    if (!empty($email) && $email !== $user['email']) {
        $stmt = $conn->prepare("SELECT id FROM alumni_users WHERE email = ? AND id != ?");
        $stmt->bind_param("si", $email, $user_id);
        $stmt->execute();
        if ($stmt->get_result()->num_rows > 0) {
            $errors[] = "Email address is already in use.";
        }
        $stmt->close();
    }

    // Validate LinkedIn URL if provided
    if (!empty($linkedin) && !filter_var($linkedin, FILTER_VALIDATE_URL)) {
        $errors[] = "Invalid LinkedIn URL format.";
    }

    // Validate phone number if provided
    if (!empty($phone) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,15}$/', $phone)) {
        $errors[] = "Invalid phone number format.";
    }

    if (empty($errors)) {
        $stmt = $conn->prepare("UPDATE alumni_users SET full_name = ?, email = ?, phone = ?, job_title = ?, linkedin = ?, location = ?, visibility = ? WHERE id = ?");
        $stmt->bind_param("sssssssi", $full_name, $email, $phone, $job_title, $linkedin, $location, $visibility, $user_id);

        if ($stmt->execute()) {
            $success_message = "Profile updated successfully!";
            // Refresh user data
            $user['full_name'] = $full_name;
            $user['email'] = $email;
            $user['phone'] = $phone;
            $user['job_title'] = $job_title;
            $user['linkedin'] = $linkedin;
            $user['location'] = $location;
            $user['visibility'] = $visibility;
        } else {
            $errors[] = "Failed to update profile. Please try again.";
        }
        $stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Profile - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">Edit Profile</h1>
            <a href="view.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Profile
            </a>
        </div>

        <div class="max-w-4xl mx-auto">
            <!-- Success Message -->
            <?php if (!empty($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">✅</span>
                        <div>
                            <strong>Success!</strong>
                            <p><?php echo htmlspecialchars($success_message); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">❌</span>
                        <div>
                            <strong>Error!</strong>
                            <ul class="mt-1">
                                <?php foreach ($errors as $error): ?>
                                    <li>• <?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Edit Form -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                    <h2 class="text-2xl font-bold mb-2">Update Your Profile</h2>
                    <p class="text-blue-100">Keep your information current to stay connected with fellow alumni</p>
                </div>

                <form action="edit.php" method="post" class="p-8">
                    <div class="grid md:grid-cols-2 gap-8">
                        <!-- Personal Information -->
                        <div>
                            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                                👤 Personal Information
                            </h3>

                            <div class="space-y-6">
                                <div>
                                    <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Full Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="full_name"
                                           name="full_name"
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           required>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Email Address <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email"
                                           id="email"
                                           name="email"
                                           value="<?php echo htmlspecialchars($user['email']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           required>
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Phone Number
                                    </label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo htmlspecialchars($user['phone']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="+234 xxx xxx xxxx">
                                </div>

                                <div>
                                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                        Current Location
                                    </label>
                                    <input type="text"
                                           id="location"
                                           name="location"
                                           value="<?php echo htmlspecialchars($user['location']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="City, State, Country">
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div>
                            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                                💼 Professional Information
                            </h3>

                            <div class="space-y-6">
                                <div>
                                    <label for="job_title" class="block text-sm font-medium text-gray-700 mb-2">
                                        Current Job Title
                                    </label>
                                    <input type="text"
                                           id="job_title"
                                           name="job_title"
                                           value="<?php echo htmlspecialchars($user['job_title']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="e.g., Software Engineer, Marketing Manager">
                                </div>

                                <div>
                                    <label for="linkedin" class="block text-sm font-medium text-gray-700 mb-2">
                                        LinkedIn Profile URL
                                    </label>
                                    <input type="url"
                                           id="linkedin"
                                           name="linkedin"
                                           value="<?php echo htmlspecialchars($user['linkedin']); ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="https://linkedin.com/in/yourprofile">
                                </div>

                                <div>
                                    <label for="visibility" class="block text-sm font-medium text-gray-700 mb-2">
                                        Profile Visibility
                                    </label>
                                    <select id="visibility"
                                            name="visibility"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="private" <?php echo ($user['visibility'] === 'private') ? 'selected' : ''; ?>>
                                            🔒 Private - Only visible to me
                                        </option>
                                        <option value="public" <?php echo ($user['visibility'] === 'public') ? 'selected' : ''; ?>>
                                            🌐 Public - Visible to other alumni
                                        </option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">
                                        Public profiles appear in the alumni directory and can be found by other members
                                    </p>
                                </div>

                                <!-- Read-only Academic Info -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h4 class="font-semibold text-gray-700 mb-3">Academic Information (Read-only)</h4>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Department:</span>
                                            <span class="font-medium"><?php echo htmlspecialchars($user['department']); ?></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Graduation Year:</span>
                                            <span class="font-medium"><?php echo htmlspecialchars($user['year_of_graduation']); ?></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Matric Number:</span>
                                            <span class="font-medium"><?php echo htmlspecialchars($user['matric_no']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="mt-8 pt-8 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="text-red-500">*</span> Required fields
                            </div>
                            <div class="flex space-x-4">
                                <a href="view.php" class="bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 transition duration-300">
                                    Cancel
                                </a>
                                <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300 font-semibold">
                                    💾 Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
