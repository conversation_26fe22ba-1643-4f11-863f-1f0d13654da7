<?php
session_start();
require_once 'includes/db_connect.php';

$errors = [];
$success_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize and validate inputs
    $matric_no = trim($_POST['matric_no']);
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $department = trim($_POST['department']);
    $year_of_graduation = trim($_POST['year_of_graduation']);
    $phone = trim($_POST['phone']);
    $job_title = trim($_POST['job_title']);
    $linkedin = trim($_POST['linkedin']);
    $location = trim($_POST['location']);

    // Basic validation
    if (empty($matric_no)) $errors[] = "Matric number is required.";
    if (empty($full_name)) $errors[] = "Full name is required.";
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "A valid email is required.";
    if (empty($password)) $errors[] = "Password is required.";
    if (empty($department)) $errors[] = "Department is required.";
    if (empty($year_of_graduation)) $errors[] = "Year of graduation is required.";

    // Certificate upload handling
    if (isset($_FILES['certificate']) && $_FILES['certificate']['error'] == 0) {
        $target_dir = "uploads/certificates/";
        $file_name = basename($_FILES["certificate"]["name"]);
        $target_file = $target_dir . time() . '_' . $file_name;
        $file_type = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));

        // Check if file is a valid image or PDF
        $allowed_types = ['jpg', 'jpeg', 'png', 'pdf'];
        if (!in_array($file_type, $allowed_types)) {
            $errors[] = "Sorry, only JPG, JPEG, PNG & PDF files are allowed.";
        }

        // Check file size (e.g., 5MB limit)
        if ($_FILES["certificate"]["size"] > 5000000) {
            $errors[] = "Sorry, your file is too large.";
        }
    } else {
        $errors[] = "Certificate upload is required.";
    }

    // If no errors, proceed with database insertion
    if (empty($errors)) {
        // Check for duplicate matric number or email
        $stmt = $conn->prepare("SELECT id FROM alumni_users WHERE matric_no = ? OR email = ?");
        $stmt->bind_param("ss", $matric_no, $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $errors[] = "An account with this matric number or email already exists.";
        } else {
            // Hash the password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Move uploaded file
            if (move_uploaded_file($_FILES["certificate"]["tmp_name"], $target_file)) {
                $certificate_path = $target_file;

                // Insert into database
                $stmt = $conn->prepare("INSERT INTO alumni_users (matric_no, full_name, email, password, department, year_of_graduation, phone, job_title, linkedin, location, certificate_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("sssssssssss", $matric_no, $full_name, $email, $hashed_password, $department, $year_of_graduation, $phone, $job_title, $linkedin, $location, $certificate_path);

                if ($stmt->execute()) {
                    $success_message = "Registration successful! Your account is pending approval from the administrator.";
                    // Clear form fields or redirect
                } else {
                    $errors[] = "Registration failed. Please try again.";
                }
            } else {
                $errors[] = "Sorry, there was an error uploading your file.";
            }
        }
        $stmt->close();
    }
    $conn->close();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Registration - Polytechnic Ilaro</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
        .floating-label {
            position: absolute;
            top: 0;
            left: 0;
            padding: 0.5rem;
            pointer-events: none;
            transition: all 0.2s ease-out;
        }
        input:focus + .floating-label,
        input:not(:placeholder-shown) + .floating-label {
            transform: translateY(-1.5rem) scale(0.75);
            color: #4F46E5;
        }
    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">

    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.php" class="text-2xl font-bold">Polytechnic Ilaro Alumni</a>
            <nav>
                <a href="login.php" class="bg-white text-blue-600 font-bold py-2 px-4 rounded hover:bg-gray-200 transition duration-300">Login</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow container mx-auto mt-10 p-4 flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-2xl">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-6">Create Your Alumni Profile</h2>
            
            <?php if (!empty($errors)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline"><?php echo $success_message; ?></span>
                </div>
            <?php else: ?>
                <!-- Registration Form -->
                <form action="register.php" method="POST" enctype="multipart/form-data" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Column 1 -->
                    <div class="space-y-6">
                        <div class="relative">
                            <input type="text" name="matric_no" id="matric_no" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="matric_no" class="floating-label text-gray-500">Matric Number</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="full_name" id="full_name" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="full_name" class="floating-label text-gray-500">Full Name</label>
                        </div>
                        <div class="relative">
                            <input type="email" name="email" id="email" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="email" class="floating-label text-gray-500">Email Address</label>
                        </div>
                        <div class="relative">
                            <input type="password" name="password" id="password" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="password" class="floating-label text-gray-500">Password</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="department" id="department" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="department" class="floating-label text-gray-500">Department</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="year_of_graduation" id="year_of_graduation" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <label for="year_of_graduation" class="floating-label text-gray-500">Year of Graduation</label>
                        </div>
                    </div>

                    <!-- Column 2 -->
                    <div class="space-y-6">
                        <div class="relative">
                            <input type="text" name="phone" id="phone" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <label for="phone" class="floating-label text-gray-500">Phone Number</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="job_title" id="job_title" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <label for="job_title" class="floating-label text-gray-500">Current Job Title</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="linkedin" id="linkedin" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <label for="linkedin" class="floating-label text-gray-500">LinkedIn Profile URL</label>
                        </div>
                        <div class="relative">
                            <input type="text" name="location" id="location" placeholder=" " class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <label for="location" class="floating-label text-gray-500">Current Location (e.g., City, Country)</label>
                        </div>
                        <div>
                            <label for="certificate" class="block text-sm font-medium text-gray-700 mb-2">Proof of Graduation (Certificate or ID)</label>
                            <input type="file" name="certificate" id="certificate" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" required>
                            <p class="text-xs text-gray-500 mt-1">Please upload your certificate or student ID for verification.</p>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="md:col-span-2">
                        <button type="submit" class="w-full gradient-bg text-white font-bold py-3 px-4 rounded-md hover:opacity-90 transition duration-300">
                            Register and Await Approval
                        </button>
                    </div>
                </form>
            <?php endif; ?>

            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    Already have an account? <a href="login.php" class="text-blue-600 hover:underline font-semibold">Login here</a>
                </p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-auto">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

</body>
</html>
