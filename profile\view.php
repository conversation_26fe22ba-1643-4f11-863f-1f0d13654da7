<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

$user_id = $_SESSION['user_id'];

// Fetch user profile data
$stmt = $conn->prepare("SELECT * FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user) {
    header('Location: ../logout.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">My Profile</h1>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- Profile Card -->
            <div class="md:col-span-1">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <!-- Profile Header -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-8 text-white text-center">
                        <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <span class="text-3xl font-bold">
                                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                            </span>
                        </div>
                        <h2 class="text-2xl font-bold"><?php echo htmlspecialchars($user['full_name']); ?></h2>
                        <p class="text-blue-100 mt-1"><?php echo htmlspecialchars($user['department']); ?></p>
                        <p class="text-blue-100">Class of <?php echo htmlspecialchars($user['year_of_graduation']); ?></p>
                    </div>

                    <!-- Profile Actions -->
                    <div class="p-6">
                        <div class="space-y-3">
                            <a href="edit.php" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300 text-center block">
                                ✏️ Edit Profile
                            </a>
                            <a href="upload_certificate.php" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition duration-300 text-center block">
                                📄 Upload Certificate
                            </a>
                            <button class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition duration-300">
                                🔒 Privacy Settings
                            </button>
                        </div>

                        <!-- Account Status -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">Account Status:</span>
                                <span class="<?php echo $user['is_verified'] ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?> px-2 py-1 rounded-full text-xs">
                                    <?php echo $user['is_verified'] ? '✅ Verified' : '⏳ Pending'; ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">Profile Visibility:</span>
                                <span class="<?php echo $user['visibility'] === 'public' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'; ?> px-2 py-1 rounded-full text-xs">
                                    <?php echo $user['visibility'] === 'public' ? '🌐 Public' : '🔒 Private'; ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Member Since:</span>
                                <span class="text-xs text-gray-500">
                                    <?php echo date('M Y', strtotime($user['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="md:col-span-2">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">Profile Information</h3>

                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                                👤 Personal Information
                            </h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Full Name</label>
                                    <p class="text-gray-800 font-medium"><?php echo htmlspecialchars($user['full_name']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Email Address</label>
                                    <p class="text-gray-800"><?php echo htmlspecialchars($user['email']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Phone Number</label>
                                    <p class="text-gray-800"><?php echo !empty($user['phone']) ? htmlspecialchars($user['phone']) : 'Not provided'; ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Location</label>
                                    <p class="text-gray-800"><?php echo !empty($user['location']) ? htmlspecialchars($user['location']) : 'Not provided'; ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Academic & Professional Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                                🎓 Academic & Professional
                            </h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Department</label>
                                    <p class="text-gray-800 font-medium"><?php echo htmlspecialchars($user['department']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Graduation Year</label>
                                    <p class="text-gray-800 font-medium"><?php echo htmlspecialchars($user['year_of_graduation']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Matriculation Number</label>
                                    <p class="text-gray-800"><?php echo htmlspecialchars($user['matric_no']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Current Job Title</label>
                                    <p class="text-gray-800"><?php echo !empty($user['job_title']) ? htmlspecialchars($user['job_title']) : 'Not provided'; ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">LinkedIn Profile</label>
                                    <?php if (!empty($user['linkedin'])): ?>
                                        <a href="<?php echo htmlspecialchars($user['linkedin']); ?>" target="_blank" class="text-blue-600 hover:underline">
                                            View LinkedIn Profile →
                                        </a>
                                    <?php else: ?>
                                        <p class="text-gray-800">Not provided</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Section -->
                    <div class="mt-8 pt-8 border-t border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                            📄 Certificate
                        </h4>
                        <?php if (!empty($user['certificate_path'])): ?>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-green-500 text-2xl mr-3">📄</span>
                                        <div>
                                            <p class="text-green-800 font-medium">Certificate uploaded</p>
                                            <p class="text-green-600 text-sm">Your certificate has been uploaded and is under review</p>
                                        </div>
                                    </div>
                                    <a href="../<?php echo htmlspecialchars($user['certificate_path']); ?>" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-300 text-sm">
                                        View Certificate
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-yellow-500 text-2xl mr-3">⚠️</span>
                                        <div>
                                            <p class="text-yellow-800 font-medium">No certificate uploaded</p>
                                            <p class="text-yellow-600 text-sm">Please upload your graduation certificate for verification</p>
                                        </div>
                                    </div>
                                    <a href="upload_certificate.php" class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition duration-300 text-sm">
                                        Upload Now
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
