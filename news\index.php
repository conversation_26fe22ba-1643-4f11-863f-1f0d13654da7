<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Fetch all news articles, ordered by most recent
$stmt = $conn->prepare("SELECT id, title, body, published_at FROM news ORDER BY published_at DESC");
$stmt->execute();
$result = $stmt->get_result();
$news_articles = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News and Announcements - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">News and Announcements</h1>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <?php if (empty($news_articles)): ?>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-gray-500 text-6xl mb-4">📰</div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">No News Available</h3>
                <p class="text-gray-600">There are currently no news articles to display. Check back later for updates!</p>
            </div>
        <?php else: ?>
            <div class="space-y-6">
                <?php foreach ($news_articles as $article): ?>
                    <article class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                        <div class="flex justify-between items-start mb-4">
                            <h2 class="text-2xl font-bold text-gray-800 hover:text-blue-600 transition duration-300">
                                <?php echo htmlspecialchars($article['title']); ?>
                            </h2>
                            <span class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                <?php echo date('M j, Y', strtotime($article['published_at'])); ?>
                            </span>
                        </div>
                        <div class="text-gray-700 leading-relaxed">
                            <?php echo nl2br(htmlspecialchars($article['body'])); ?>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <span class="text-xs text-gray-500">
                                Published on <?php echo date('F j, Y \a\t g:i A', strtotime($article['published_at'])); ?>
                            </span>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
