<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Get event ID from URL
$event_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($event_id <= 0) {
    header('Location: index.php');
    exit();
}

// Fetch event details
$stmt = $conn->prepare("SELECT id, title, description, date, image_path FROM events WHERE id = ?");
$stmt->bind_param("i", $event_id);
$stmt->execute();
$result = $stmt->get_result();
$event = $result->fetch_assoc();
$stmt->close();

if (!$event) {
    header('Location: index.php');
    exit();
}

$is_upcoming = $event['date'] >= date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event['title']); ?> - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">Event Details</h1>
            <a href="index.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Events
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Event Image -->
            <?php if ($event['image_path']): ?>
                <div class="w-full h-64 md:h-96 overflow-hidden">
                    <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                         alt="<?php echo htmlspecialchars($event['title']); ?>"
                         class="w-full h-full object-cover">
                </div>
            <?php else: ?>
                <div class="w-full h-64 md:h-96 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <span class="text-white text-8xl">📅</span>
                </div>
            <?php endif; ?>

            <!-- Event Content -->
            <div class="p-8">
                <div class="flex flex-col md:flex-row md:justify-between md:items-start mb-6">
                    <div class="mb-4 md:mb-0">
                        <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
                            <?php echo htmlspecialchars($event['title']); ?>
                        </h1>
                        <div class="flex items-center text-gray-600">
                            <span class="text-2xl mr-2">📅</span>
                            <span class="text-lg">
                                <?php echo date('l, F j, Y', strtotime($event['date'])); ?>
                            </span>
                        </div>
                    </div>
                    <div class="flex flex-col items-start md:items-end">
                        <span class="<?php echo $is_upcoming ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?> px-4 py-2 rounded-full text-sm font-semibold mb-2">
                            <?php echo $is_upcoming ? 'Upcoming Event' : 'Past Event'; ?>
                        </span>
                        <?php if ($is_upcoming): ?>
                            <div class="text-sm text-gray-600">
                                <?php
                                $days_until = (strtotime($event['date']) - time()) / (60 * 60 * 24);
                                if ($days_until < 1) {
                                    echo "Today!";
                                } elseif ($days_until < 2) {
                                    echo "Tomorrow";
                                } else {
                                    echo ceil($days_until) . " days to go";
                                }
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Event Description -->
                <div class="prose max-w-none">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">About This Event</h2>
                    <div class="text-gray-700 leading-relaxed text-lg">
                        <?php echo nl2br(htmlspecialchars($event['description'])); ?>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <?php if ($is_upcoming): ?>
                            <button class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition duration-300 font-semibold">
                                📅 Add to Calendar
                            </button>
                            <button class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300 font-semibold">
                                📧 Get Reminders
                            </button>
                        <?php endif; ?>
                        <button class="bg-gray-600 text-white px-6 py-3 rounded-md hover:bg-gray-700 transition duration-300 font-semibold">
                            📤 Share Event
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Events -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Other Events</h2>
            <div class="text-center py-8">
                <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300 font-semibold">
                    View All Events
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>
</body>
</html>
