// Custom JS for AJAX handlers and other dynamic functionality.

document.addEventListener('DOMContentLoaded', function() {
    const chatForm = document.getElementById('chat-form');
    if (chatForm) {
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const messageInput = document.getElementById('message');
            const message = messageInput.value;
            if (message.trim() === '') {
                return;
            }

            // AJAX call to send_message.php
            fetch('../chat/send_message.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message=' + encodeURIComponent(message)
            })
            .then(response => response.text())
            .then(data => {
                messageInput.value = '';
                loadChatMessages();
            });
        });
    }

    if (document.getElementById('chat-messages')) {
        loadChatMessages();
        setInterval(loadChatMessages, 5000); // Refresh every 5 seconds
    }
});

function loadChatMessages() {
    fetch('../ajax/load_chat.php')
        .then(response => response.text())
        .then(data => {
            const chatMessages = document.getElementById('chat-messages');
            if(chatMessages){
                chatMessages.innerHTML = data;
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        });
}
