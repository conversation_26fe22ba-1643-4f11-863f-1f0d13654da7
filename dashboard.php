<?php
require_once 'includes/session.php';
require_login('alumni');
require_once 'includes/db_connect.php';

$user_id = $_SESSION['user_id'];

// Fetch user data
$stmt = $conn->prepare("SELECT full_name, department, year_of_graduation FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($full_name, $department, $year_of_graduation);
$stmt->fetch();
$stmt->close();

// Fetch latest news and events (placeholders for now)
$latest_news = "No news yet.";
$upcoming_events = "No upcoming events.";

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Dashboard</h1>
            <nav>
                <a href="profile/view.php" class="hover:underline">My Profile</a>
                <a href="logout.php" class="ml-4 bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <h2 class="text-3xl font-bold text-gray-800 mb-6">Welcome, <?php echo htmlspecialchars($full_name); ?>!</h2>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- Left Sidebar -->
            <div class="md:col-span-1 space-y-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-blue-600 mb-4">My Information</h3>
                    <p><strong>Department:</strong> <?php echo htmlspecialchars($department); ?></p>
                    <p><strong>Graduation Year:</strong> <?php echo htmlspecialchars($year_of_graduation); ?></p>
                    <a href="profile/edit.php" class="text-blue-600 hover:underline mt-4 inline-block">Edit Profile</a>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-blue-600 mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="search/alumni_directory.php" class="hover:text-blue-600">Alumni Directory</a></li>
                        <li><a href="events/index.php" class="hover:text-blue-600">Events</a></li>
                        <li><a href="news/index.php" class="hover:text-blue-600">News & Announcements</a></li>
                        <li><a href="gallery/index.php" class="hover:text-blue-600">Gallery</a></li>
                        <li><a href="chat/chatbox.php" class="hover:text-blue-600">Chat Forum</a></li>
                        <li><a href="feedback/submit.php" class="hover:text-blue-600">Submit Feedback</a></li>
                    </ul>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="md:col-span-2 space-y-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-green-600 mb-4">Latest News</h3>
                    <p><?php echo $latest_news; ?></p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-orange-500 mb-4">Upcoming Events</h3>
                    <p><?php echo $upcoming_events; ?></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

</body>
</html>
