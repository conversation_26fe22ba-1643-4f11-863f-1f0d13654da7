<?php
require_once 'includes/session.php';
require_login('alumni');
require_once 'includes/db_connect.php';

$user_id = $_SESSION['user_id'];

// Fetch user data
$stmt = $conn->prepare("SELECT full_name, department, year_of_graduation FROM alumni_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($full_name, $department, $year_of_graduation);
$stmt->fetch();
$stmt->close();

// Fetch latest news
$latest_news = "No news yet.";
$stmt = $conn->prepare("SELECT title, body, published_at FROM news ORDER BY published_at DESC LIMIT 3");
$stmt->execute();
$result = $stmt->get_result();
$news_articles = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Fetch upcoming events
$upcoming_events = "No upcoming events.";
$stmt = $conn->prepare("SELECT title, description, date FROM events WHERE date >= CURDATE() ORDER BY date ASC LIMIT 3");
$stmt->execute();
$result = $stmt->get_result();
$events = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get dashboard statistics
$stats = [];

// Total alumni count
$result = $conn->query("SELECT COUNT(*) as count FROM alumni_users WHERE is_verified = 1");
$stats['total_alumni'] = $result ? $result->fetch_assoc()['count'] : 0;

// Total events count
$result = $conn->query("SELECT COUNT(*) as count FROM events");
$stats['total_events'] = $result ? $result->fetch_assoc()['count'] : 0;

// Recent chat messages count
$result = $conn->query("SELECT COUNT(*) as count FROM chat_messages WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stats['recent_messages'] = $result ? $result->fetch_assoc()['count'] : 0;

// Gallery images count
$result = $conn->query("SELECT COUNT(*) as count FROM media_gallery");
$stats['gallery_images'] = $result ? $result->fetch_assoc()['count'] : 0;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Dashboard</h1>
            <nav>
                <a href="profile/view.php" class="hover:underline">My Profile</a>
                <a href="logout.php" class="ml-4 bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-4xl font-bold mb-2">Welcome back, <?php echo htmlspecialchars($full_name); ?>!</h2>
                    <p class="text-blue-100">
                        <?php echo htmlspecialchars($department); ?> • Class of <?php echo htmlspecialchars($year_of_graduation); ?>
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-3xl font-bold">
                            <?php echo strtoupper(substr($full_name, 0, 1)); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white p-4 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total_alumni']; ?></div>
                <div class="text-sm text-gray-600">Alumni Members</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-green-600"><?php echo $stats['total_events']; ?></div>
                <div class="text-sm text-gray-600">Total Events</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-purple-600"><?php echo $stats['recent_messages']; ?></div>
                <div class="text-sm text-gray-600">Messages (7 days)</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-md text-center">
                <div class="text-2xl font-bold text-orange-600"><?php echo $stats['gallery_images']; ?></div>
                <div class="text-sm text-gray-600">Gallery Photos</div>
            </div>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- Left Sidebar -->
            <div class="md:col-span-1 space-y-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-blue-600 mb-4 flex items-center">
                        👤 My Profile
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 mr-2">🎓</span>
                            <span class="text-sm"><?php echo htmlspecialchars($department); ?></span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <span class="text-green-500 mr-2">📅</span>
                            <span class="text-sm">Class of <?php echo htmlspecialchars($year_of_graduation); ?></span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <a href="profile/edit.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300 text-sm">
                            Edit Profile
                        </a>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-blue-600 mb-4 flex items-center">
                        🚀 Quick Actions
                    </h3>
                    <div class="grid grid-cols-2 gap-3">
                        <a href="search/alumni_directory.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">👥</div>
                            <div class="text-xs text-gray-600">Directory</div>
                        </a>
                        <a href="events/index.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">📅</div>
                            <div class="text-xs text-gray-600">Events</div>
                        </a>
                        <a href="news/index.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">📰</div>
                            <div class="text-xs text-gray-600">News</div>
                        </a>
                        <a href="gallery/index.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">🖼️</div>
                            <div class="text-xs text-gray-600">Gallery</div>
                        </a>
                        <a href="chat/chatbox.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">💬</div>
                            <div class="text-xs text-gray-600">Chat</div>
                        </a>
                        <a href="feedback/submit.php" class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition duration-300">
                            <div class="text-2xl mb-1">📝</div>
                            <div class="text-xs text-gray-600">Feedback</div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="md:col-span-2 space-y-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-green-600">Latest News</h3>
                        <a href="news/index.php" class="text-sm text-blue-600 hover:underline">View All →</a>
                    </div>
                    <?php if (empty($news_articles)): ?>
                        <p class="text-gray-600">No news available at the moment.</p>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($news_articles as $article): ?>
                                <div class="border-l-4 border-green-500 pl-4">
                                    <h4 class="font-semibold text-gray-800"><?php echo htmlspecialchars($article['title']); ?></h4>
                                    <p class="text-gray-600 text-sm mt-1">
                                        <?php echo substr(htmlspecialchars($article['body']), 0, 150) . (strlen($article['body']) > 150 ? '...' : ''); ?>
                                    </p>
                                    <span class="text-xs text-gray-500"><?php echo date('M j, Y', strtotime($article['published_at'])); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-orange-500">Upcoming Events</h3>
                        <a href="events/index.php" class="text-sm text-blue-600 hover:underline">View All →</a>
                    </div>
                    <?php if (empty($events)): ?>
                        <p class="text-gray-600">No upcoming events scheduled.</p>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($events as $event): ?>
                                <div class="border-l-4 border-orange-500 pl-4">
                                    <h4 class="font-semibold text-gray-800"><?php echo htmlspecialchars($event['title']); ?></h4>
                                    <p class="text-gray-600 text-sm mt-1">
                                        <?php echo substr(htmlspecialchars($event['description']), 0, 150) . (strlen($event['description']) > 150 ? '...' : ''); ?>
                                    </p>
                                    <span class="text-xs text-gray-500">📅 <?php echo date('M j, Y', strtotime($event['date'])); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

</body>
</html>
