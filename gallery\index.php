<?php
require_once '../includes/session.php';
require_login('alumni');
require_once '../includes/db_connect.php';

// Fetch all gallery images, ordered by most recent
$stmt = $conn->prepare("SELECT id, title, file_path, uploaded_at FROM media_gallery ORDER BY uploaded_at DESC");
$stmt->execute();
$result = $stmt->get_result();
$gallery_images = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery - Alumni Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(90deg, #2563EB, #4F46E5);
        }
        .gallery-item {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            width: 80%;
            max-width: 700px;
            max-height: 80%;
            object-fit: contain;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #bbb;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Alumni Portal</h1>
            <nav>
                <a href="../dashboard.php" class="hover:underline mr-4">Dashboard</a>
                <a href="../profile/view.php" class="hover:underline mr-4">My Profile</a>
                <a href="../logout.php" class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto mt-10 p-4">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800">Photo Gallery</h1>
            <a href="../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                ← Back to Dashboard
            </a>
        </div>

        <?php if (empty($gallery_images)): ?>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-gray-500 text-6xl mb-4">🖼️</div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">No Images Available</h3>
                <p class="text-gray-600">The gallery is currently empty. Check back later for new photos!</p>
            </div>
        <?php else: ?>
            <!-- Gallery Stats -->
            <div class="bg-white p-4 rounded-lg shadow-md mb-8">
                <div class="flex items-center justify-center">
                    <span class="text-gray-600">
                        📸 <strong><?php echo count($gallery_images); ?></strong> photos in gallery
                    </span>
                </div>
            </div>

            <!-- Gallery Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                <?php foreach ($gallery_images as $index => $image): ?>
                    <div class="gallery-item bg-white rounded-lg shadow-md overflow-hidden cursor-pointer"
                         onclick="openModal('<?php echo htmlspecialchars($image['file_path']); ?>', '<?php echo htmlspecialchars($image['title']); ?>')">
                        <div class="aspect-w-1 aspect-h-1 w-full h-64 overflow-hidden">
                            <img src="../<?php echo htmlspecialchars($image['file_path']); ?>"
                                 alt="<?php echo htmlspecialchars($image['title']); ?>"
                                 class="w-full h-full object-cover hover:scale-105 transition duration-300">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-800 mb-2 truncate">
                                <?php echo htmlspecialchars($image['title']); ?>
                            </h3>
                            <p class="text-sm text-gray-500">
                                📅 <?php echo date('M j, Y', strtotime($image['uploaded_at'])); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Load More Button (if needed for pagination) -->
            <?php if (count($gallery_images) >= 20): ?>
                <div class="text-center mt-8">
                    <button class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300">
                        Load More Photos
                    </button>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>

    <!-- Image Modal -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
        <div id="caption" class="text-center text-white mt-4 text-lg"></div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 mt-10">
        <div class="container mx-auto text-center">
            <p>&copy; 2025 Polytechnic Ilaro Alumni Network. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        function openModal(imagePath, title) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const caption = document.getElementById('caption');

            modal.style.display = 'block';
            modalImg.src = '../' + imagePath;
            caption.innerHTML = title;
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
